# RSS插件SQLite数据库重构总结

## 重构概述

本次重构将RSS插件的数据存储从JSON文件迁移到SQLite数据库，实现了更高效、更可靠的数据管理。

## 数据库架构设计

### 表结构

#### 1. rsshub_endpoints - RSSHub端点表
```sql
CREATE TABLE rsshub_endpoints (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT UNIQUE NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
)
```
- **功能**: 独立管理RSSHub端点
- **特点**: 全局共享，不与特定用户绑定

#### 2. rss_feeds - RSS源表
```sql
CREATE TABLE rss_feeds (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    description TEXT,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
)
```
- **功能**: 存储RSS源的基本信息
- **特点**: 多个用户可以共享同一个RSS源

#### 3. subscriptions - 订阅表
```sql
CREATE TABLE subscriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    feed_id INTEGER NOT NULL,
    cron_expr TEXT NOT NULL,
    last_check_time INTEGER DEFAULT 0,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE,
    UNIQUE(user_id, feed_id)
)
```
- **功能**: 管理用户/群组的订阅关系
- **特点**: 每个用户/群组的推送时间独立

#### 4. rss_items - RSS内容表
```sql
CREATE TABLE rss_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    feed_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    link TEXT NOT NULL,
    description TEXT,
    pub_date TEXT,
    pub_date_timestamp INTEGER,
    pic_urls TEXT,
    content_hash TEXT UNIQUE NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE
)
```
- **功能**: 存储解析的RSS内容
- **特点**: 使用content_hash去重，避免重复存储

#### 5. push_records - 推送记录表
```sql
CREATE TABLE push_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    item_id INTEGER NOT NULL,
    pushed_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (item_id) REFERENCES rss_items (id) ON DELETE CASCADE,
    UNIQUE(user_id, item_id)
)
```
- **功能**: 记录推送状态，避免重复推送
- **特点**: 精确控制每个用户的推送状态

## 核心功能实现

### 1. RSSHub端点管理
- `add_rsshub_endpoint()` - 添加端点
- `get_rsshub_endpoints()` - 获取所有端点
- `remove_rsshub_endpoint()` - 删除端点
- `get_rsshub_endpoint_by_index()` - 根据索引获取端点

### 2. RSS源管理
- `add_or_update_feed()` - 添加或更新RSS源
- `get_feed_by_url()` - 根据URL获取RSS源
- `get_feed_by_id()` - 根据ID获取RSS源

### 3. 订阅管理
- `add_subscription()` - 添加订阅
- `remove_subscription()` - 删除订阅
- `get_user_subscriptions()` - 获取用户订阅
- `get_all_subscriptions()` - 获取所有订阅（用于定时任务）
- `update_subscription_check_time()` - 更新检查时间

### 4. RSS内容管理
- `save_rss_item()` - 保存RSS条目（自动去重）
- `get_new_items_for_user()` - 获取用户未推送的新条目
- `get_latest_item_for_feed()` - 获取RSS源的最新条目

### 5. 推送记录管理
- `mark_item_as_pushed()` - 标记条目已推送
- `is_item_pushed_to_user()` - 检查推送状态
- `cleanup_old_push_records()` - 清理旧推送记录

### 6. 数据迁移
- `migrate_from_json()` - 从旧JSON数据自动迁移
- 自动备份旧数据文件

## 主要改进

### 1. 数据结构优化
- **分离关注点**: RSSHub端点、RSS源、订阅、内容、推送记录分别管理
- **数据共享**: 多用户可共享同一RSS源，减少重复数据
- **关系完整性**: 使用外键约束保证数据一致性

### 2. 推送逻辑优化
- **精确去重**: 基于内容哈希避免重复推送
- **独立时间**: 每个用户的推送时间独立管理
- **状态追踪**: 精确记录每个用户的推送状态

### 3. 性能优化
- **索引优化**: 为常用查询字段创建索引
- **批量操作**: 支持批量数据处理
- **内存效率**: 避免加载全部数据到内存

### 4. 可维护性提升
- **模块化设计**: 数据访问逻辑集中在DataHandler
- **错误处理**: 完善的异常处理和日志记录
- **向后兼容**: 保持原有API接口兼容

## 迁移过程

### 自动迁移
1. 检测旧JSON数据文件
2. 解析并迁移RSSHub端点
3. 迁移RSS源和订阅关系
4. 备份原数据文件
5. 记录迁移日志

### 手动迁移（如需要）
```python
from data_handler import DataHandler

handler = DataHandler()
handler.migrate_from_json("path/to/old/data.json")
```

## 使用示例

### 添加订阅
```python
# 添加RSS源
feed_id = handler.add_or_update_feed(url, title, description)

# 添加用户订阅
handler.add_subscription(user_id, feed_id, cron_expr)

# 拉取并保存RSS内容
await poll_and_save_rss(url, feed_id)

# 获取用户未推送的新内容
new_items = handler.get_new_items_for_user(user_id, feed_id, last_check_time)

# 推送后标记已推送
for item in new_items:
    handler.mark_item_as_pushed(user_id, item['id'])
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_sqlite_migration.py
```

测试覆盖：
- 数据库初始化
- RSSHub端点管理
- RSS源管理
- 订阅管理
- RSS内容管理
- 推送记录管理
- JSON数据迁移

## 文件变更清单

### 修改的文件
- ✅ `data_handler.py` - 完全重写，使用SQLite
- ✅ `main.py` - 更新数据访问逻辑
- ✅ `SQLITE_MIGRATION_SUMMARY.md` - 新增重构说明文档
- ✅ `test_sqlite_migration.py` - 新增测试脚本

### 保持不变的文件
- ✅ `rss.py` - RSSItem数据类
- ✅ `pic_handler.py` - 图片处理逻辑
- ✅ `_conf_schema.json` - 配置文件
- ✅ `requirements.txt` - 依赖文件（SQLite是内置模块）

## 兼容性说明

- **向后兼容**: 保持所有原有命令和功能
- **自动迁移**: 首次启动时自动从JSON迁移
- **数据安全**: 迁移前自动备份原数据
- **渐进升级**: 可以逐步验证新功能

## 性能提升

- **查询效率**: 使用索引优化常用查询
- **内存使用**: 按需加载数据，不再全量加载
- **并发安全**: SQLite提供事务支持
- **数据完整性**: 外键约束保证数据一致性

## 未来扩展

基于新的数据库架构，可以轻松扩展：
- 统计功能（订阅数量、推送统计等）
- 用户偏好设置
- RSS源质量评估
- 推送历史查询
- 数据导出/导入功能
