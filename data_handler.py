import os
import sqlite3
import json
import time
import hashlib
from urllib.parse import urlparse
from lxml import etree
from bs4 import BeautifulSoup
import re
from typing import List, Dict, Optional, Tuple
import logging

class DataHandler:
    def __init__(self, db_path="data/astrbot_plugin_rss.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("astrbot")
        self._ensure_data_dir()
        self._init_database()

    def _ensure_data_dir(self):
        """确保数据目录存在"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)

    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # RSSHub端点表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rsshub_endpoints (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
                )
            ''')

            # RSS源表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rss_feeds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    title TEXT,
                    description TEXT,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
                )
            ''')

            # 订阅表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    feed_id INTEGER NOT NULL,
                    cron_expr TEXT NOT NULL,
                    last_check_time INTEGER DEFAULT 0,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE,
                    UNIQUE(user_id, feed_id)
                )
            ''')

            # RSS内容表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rss_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    feed_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    link TEXT NOT NULL,
                    description TEXT,
                    pub_date TEXT,
                    pub_date_timestamp INTEGER,
                    pic_urls TEXT,
                    content_hash TEXT UNIQUE NOT NULL,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE
                )
            ''')

            # 推送记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS push_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    item_id INTEGER NOT NULL,
                    pushed_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (item_id) REFERENCES rss_items (id) ON DELETE CASCADE,
                    UNIQUE(user_id, item_id)
                )
            ''')

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_rss_items_feed_id ON rss_items(feed_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_rss_items_pub_date ON rss_items(pub_date_timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_push_records_user_item ON push_records(user_id, item_id)')

            conn.commit()
            self.logger.info("数据库初始化完成")

    # ==================== RSSHub端点管理 ====================

    def add_rsshub_endpoint(self, url: str) -> bool:
        """添加RSSHub端点"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('INSERT INTO rsshub_endpoints (url) VALUES (?)', (url,))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False  # URL已存在
        except Exception as e:
            self.logger.error(f"添加RSSHub端点失败: {e}")
            return False

    def get_rsshub_endpoints(self) -> List[Dict]:
        """获取所有RSSHub端点"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, url, created_at FROM rsshub_endpoints ORDER BY id')
            rows = cursor.fetchall()
            return [{'id': row[0], 'url': row[1], 'created_at': row[2]} for row in rows]

    def remove_rsshub_endpoint(self, endpoint_id: int) -> bool:
        """删除RSSHub端点"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM rsshub_endpoints WHERE id = ?', (endpoint_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            self.logger.error(f"删除RSSHub端点失败: {e}")
            return False

    def get_rsshub_endpoint_by_index(self, index: int) -> Optional[str]:
        """根据索引获取RSSHub端点URL"""
        endpoints = self.get_rsshub_endpoints()
        if 0 <= index < len(endpoints):
            return endpoints[index]['url']
        return None

    # ==================== RSS源管理 ====================

    def add_or_update_feed(self, url: str, title: str = None, description: str = None) -> int:
        """添加或更新RSS源，返回feed_id"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否已存在
                cursor.execute('SELECT id FROM rss_feeds WHERE url = ?', (url,))
                row = cursor.fetchone()

                if row:
                    # 更新现有记录
                    feed_id = row[0]
                    if title or description:
                        cursor.execute('''
                            UPDATE rss_feeds
                            SET title = COALESCE(?, title),
                                description = COALESCE(?, description),
                                updated_at = strftime('%s', 'now')
                            WHERE id = ?
                        ''', (title, description, feed_id))
                        conn.commit()
                else:
                    # 插入新记录
                    cursor.execute('''
                        INSERT INTO rss_feeds (url, title, description)
                        VALUES (?, ?, ?)
                    ''', (url, title, description))
                    feed_id = cursor.lastrowid
                    conn.commit()

                return feed_id
        except Exception as e:
            self.logger.error(f"添加/更新RSS源失败: {e}")
            raise

    def get_feed_by_url(self, url: str) -> Optional[Dict]:
        """根据URL获取RSS源信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, url, title, description FROM rss_feeds WHERE url = ?', (url,))
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'url': row[1],
                    'title': row[2],
                    'description': row[3]
                }
        return None

    def get_feed_by_id(self, feed_id: int) -> Optional[Dict]:
        """根据ID获取RSS源信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id, url, title, description FROM rss_feeds WHERE id = ?', (feed_id,))
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'url': row[1],
                    'title': row[2],
                    'description': row[3]
                }
        return None

    # ==================== 订阅管理 ====================

    def add_subscription(self, user_id: str, feed_id: int, cron_expr: str) -> bool:
        """添加订阅"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO subscriptions (user_id, feed_id, cron_expr, last_check_time)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, feed_id, cron_expr, int(time.time())))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"添加订阅失败: {e}")
            return False

    def remove_subscription(self, user_id: str, feed_id: int) -> bool:
        """删除订阅"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM subscriptions WHERE user_id = ? AND feed_id = ?',
                             (user_id, feed_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            self.logger.error(f"删除订阅失败: {e}")
            return False

    def get_user_subscriptions(self, user_id: str) -> List[Dict]:
        """获取用户的所有订阅"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT s.id, s.feed_id, s.cron_expr, s.last_check_time,
                       f.url, f.title, f.description
                FROM subscriptions s
                JOIN rss_feeds f ON s.feed_id = f.id
                WHERE s.user_id = ?
                ORDER BY s.id
            ''', (user_id,))
            rows = cursor.fetchall()
            return [{
                'subscription_id': row[0],
                'feed_id': row[1],
                'cron_expr': row[2],
                'last_check_time': row[3],
                'url': row[4],
                'title': row[5],
                'description': row[6]
            } for row in rows]

    def get_all_subscriptions(self) -> List[Dict]:
        """获取所有订阅（用于定时任务）"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT s.user_id, s.feed_id, s.cron_expr, s.last_check_time,
                       f.url, f.title
                FROM subscriptions s
                JOIN rss_feeds f ON s.feed_id = f.id
            ''')
            rows = cursor.fetchall()
            return [{
                'user_id': row[0],
                'feed_id': row[1],
                'cron_expr': row[2],
                'last_check_time': row[3],
                'url': row[4],
                'title': row[5]
            } for row in rows]

    def update_subscription_check_time(self, user_id: str, feed_id: int, check_time: int = None):
        """更新订阅的最后检查时间"""
        if check_time is None:
            check_time = int(time.time())

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE subscriptions
                    SET last_check_time = ?
                    WHERE user_id = ? AND feed_id = ?
                ''', (check_time, user_id, feed_id))
                conn.commit()
        except Exception as e:
            self.logger.error(f"更新订阅检查时间失败: {e}")

    # ==================== RSS内容管理 ====================

    def _generate_content_hash(self, title: str, link: str, description: str = "") -> str:
        """生成内容哈希值，用于去重"""
        content = f"{title}|{link}|{description}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def save_rss_item(self, feed_id: int, title: str, link: str, description: str = "",
                     pub_date: str = "", pub_date_timestamp: int = 0, pic_urls: List[str] = None) -> Optional[int]:
        """保存RSS条目，返回item_id，如果已存在则返回None"""
        if pic_urls is None:
            pic_urls = []

        content_hash = self._generate_content_hash(title, link, description)
        pic_urls_json = json.dumps(pic_urls) if pic_urls else ""

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查是否已存在
                cursor.execute('SELECT id FROM rss_items WHERE content_hash = ?', (content_hash,))
                if cursor.fetchone():
                    return None  # 已存在，不重复保存

                # 插入新条目
                cursor.execute('''
                    INSERT INTO rss_items
                    (feed_id, title, link, description, pub_date, pub_date_timestamp, pic_urls, content_hash)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (feed_id, title, link, description, pub_date, pub_date_timestamp, pic_urls_json, content_hash))

                item_id = cursor.lastrowid
                conn.commit()
                return item_id

        except Exception as e:
            self.logger.error(f"保存RSS条目失败: {e}")
            return None

    def get_new_items_for_user(self, user_id: str, feed_id: int, after_timestamp: int = 0) -> List[Dict]:
        """获取用户未推送的新条目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT i.id, i.title, i.link, i.description, i.pub_date,
                       i.pub_date_timestamp, i.pic_urls, f.title as feed_title
                FROM rss_items i
                JOIN rss_feeds f ON i.feed_id = f.id
                LEFT JOIN push_records p ON i.id = p.item_id AND p.user_id = ?
                WHERE i.feed_id = ?
                  AND i.pub_date_timestamp > ?
                  AND p.id IS NULL
                ORDER BY i.pub_date_timestamp DESC
            ''', (user_id, feed_id, after_timestamp))

            rows = cursor.fetchall()
            items = []
            for row in rows:
                pic_urls = json.loads(row[6]) if row[6] else []
                items.append({
                    'id': row[0],
                    'title': row[1],
                    'link': row[2],
                    'description': row[3],
                    'pub_date': row[4],
                    'pub_date_timestamp': row[5],
                    'pic_urls': pic_urls,
                    'feed_title': row[7]
                })
            return items

    def get_latest_item_for_feed(self, feed_id: int) -> Optional[Dict]:
        """获取RSS源的最新条目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, title, link, description, pub_date, pub_date_timestamp, pic_urls
                FROM rss_items
                WHERE feed_id = ?
                ORDER BY pub_date_timestamp DESC, id DESC
                LIMIT 1
            ''', (feed_id,))

            row = cursor.fetchone()
            if row:
                pic_urls = json.loads(row[6]) if row[6] else []
                return {
                    'id': row[0],
                    'title': row[1],
                    'link': row[2],
                    'description': row[3],
                    'pub_date': row[4],
                    'pub_date_timestamp': row[5],
                    'pic_urls': pic_urls
                }
        return None

    # ==================== 推送记录管理 ====================

    def mark_item_as_pushed(self, user_id: str, item_id: int) -> bool:
        """标记条目已推送给用户"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO push_records (user_id, item_id)
                    VALUES (?, ?)
                ''', (user_id, item_id))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"标记推送记录失败: {e}")
            return False

    def is_item_pushed_to_user(self, user_id: str, item_id: int) -> bool:
        """检查条目是否已推送给用户"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT 1 FROM push_records WHERE user_id = ? AND item_id = ?',
                         (user_id, item_id))
            return cursor.fetchone() is not None

    def cleanup_old_push_records(self, days: int = 30):
        """清理旧的推送记录"""
        cutoff_time = int(time.time()) - (days * 24 * 60 * 60)
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM push_records WHERE pushed_at < ?', (cutoff_time,))
                deleted_count = cursor.rowcount
                conn.commit()
                self.logger.info(f"清理了 {deleted_count} 条旧推送记录")
        except Exception as e:
            self.logger.error(f"清理推送记录失败: {e}")

    # ==================== 兼容性方法（保持与原代码的接口兼容） ====================

    def get_subs_channel_url(self, user_id: str) -> List[str]:
        """获取用户订阅的频道URL列表（兼容性方法）"""
        subscriptions = self.get_user_subscriptions(user_id)
        return [sub['url'] for sub in subscriptions]

    def migrate_from_json(self, json_path: str = "data/astrbot_plugin_rss_data.json"):
        """从旧的JSON数据迁移到SQLite"""
        if not os.path.exists(json_path):
            self.logger.info("未找到旧的JSON数据文件，跳过迁移")
            return

        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                old_data = json.load(f)

            # 迁移RSSHub端点
            if 'rsshub_endpoints' in old_data:
                for url in old_data['rsshub_endpoints']:
                    self.add_rsshub_endpoint(url)
                self.logger.info(f"迁移了 {len(old_data['rsshub_endpoints'])} 个RSSHub端点")

            # 迁移RSS源和订阅
            migrated_feeds = 0
            migrated_subs = 0

            for url, info in old_data.items():
                if url in ['rsshub_endpoints', 'settings']:
                    continue

                # 添加RSS源
                feed_info = info.get('info', {})
                feed_id = self.add_or_update_feed(
                    url,
                    feed_info.get('title'),
                    feed_info.get('description')
                )
                migrated_feeds += 1

                # 添加订阅
                subscribers = info.get('subscribers', {})
                for user_id, sub_info in subscribers.items():
                    cron_expr = sub_info.get('cron_expr', '0 * * * *')
                    self.add_subscription(user_id, feed_id, cron_expr)

                    # 更新最后检查时间
                    last_update = sub_info.get('last_update', 0)
                    self.update_subscription_check_time(user_id, feed_id, last_update)
                    migrated_subs += 1

            self.logger.info(f"迁移完成: {migrated_feeds} 个RSS源, {migrated_subs} 个订阅")

            # 备份旧文件
            backup_path = json_path + ".backup"
            os.rename(json_path, backup_path)
            self.logger.info(f"旧数据文件已备份到: {backup_path}")

        except Exception as e:
            self.logger.error(f"数据迁移失败: {e}")
            raise

    # ==================== 工具方法 ====================

    def parse_channel_info(self, text):
        """解析RSS频道信息"""
        root = etree.fromstring(text)
        title = root.xpath("//title")[0].text
        description = root.xpath("//description")[0].text
        return title, description

    def strip_html_pic(self, html) -> List[str]:
        """解析HTML内容，提取图片地址"""
        soup = BeautifulSoup(html, "html.parser")
        ordered_content = []

        # 处理图片节点
        for img in soup.find_all('img'):
            img_src = img.get('src')
            if img_src:
                ordered_content.append(img_src)

        return ordered_content

    def strip_html(self, html):
        """去除HTML标签"""
        soup = BeautifulSoup(html, "html.parser")
        text = soup.get_text()
        return re.sub(r"\n+", "\n", text)

    def get_root_url(self, url):
        """获取URL的根域名"""
        parsed_url = urlparse(url)
        return f"{parsed_url.scheme}://{parsed_url.netloc}"
