#!/usr/bin/env python3
"""
测试RSS插件数据库功能的完整示例
"""

import os
import sys
import sqlite3
import json
import time
import asyncio
import aiohttp
from lxml import etree
from data_handler import DataHandler

class MockRSSServer:
    """模拟RSS服务器，生成测试RSS内容"""
    
    @staticmethod
    def generate_rss_xml(feed_title="测试RSS频道", num_items=3):
        """生成测试RSS XML内容"""
        current_time = int(time.time())
        
        items_xml = ""
        for i in range(num_items):
            pub_timestamp = current_time - (i * 3600)  # 每小时一条
            pub_date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime(pub_timestamp))
            
            items_xml += f"""
            <item>
                <title>测试文章 {i+1} - {int(time.time())}</title>
                <link>https://example.com/article-{i+1}-{current_time}</link>
                <description>这是测试文章 {i+1} 的描述内容，包含一些测试文本。&lt;img src="https://example.com/pic{i+1}.jpg"&gt;</description>
                <pubDate>{pub_date}</pubDate>
            </item>"""
        
        rss_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
    <channel>
        <title>{feed_title}</title>
        <description>这是一个测试RSS频道</description>
        <link>https://example.com</link>
        {items_xml}
    </channel>
</rss>"""
        
        return rss_xml

class RSSTestSuite:
    """RSS功能测试套件"""
    
    def __init__(self, db_path="test_rss_complete.db"):
        self.db_path = db_path
        self.handler = None
        self.test_results = []
    
    def setup(self):
        """设置测试环境"""
        # 清理可能存在的测试数据库
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        # 初始化数据处理器
        self.handler = DataHandler(self.db_path)
        print(f"✅ 测试环境设置完成，数据库: {self.db_path}")
    
    def cleanup(self):
        """清理测试环境"""
        if os.path.exists(self.db_path):
            try:
                os.remove(self.db_path)
                print(f"✅ 测试数据库已清理: {self.db_path}")
            except:
                print(f"⚠️ 无法删除测试数据库: {self.db_path}")
    
    def test_database_structure(self):
        """测试数据库结构"""
        print("\n🔍 测试数据库结构...")
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                expected_tables = ['rsshub_endpoints', 'rss_feeds', 'subscriptions', 'rss_items', 'push_records']
                for table in expected_tables:
                    if table in tables:
                        print(f"  ✅ 表 {table} 存在")
                    else:
                        print(f"  ❌ 表 {table} 不存在")
                        return False
                
                # 检查索引
                cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
                indexes = [row[0] for row in cursor.fetchall()]
                print(f"  📊 创建了 {len(indexes)} 个索引")
                
                return True
                
        except Exception as e:
            print(f"  ❌ 数据库结构测试失败: {e}")
            return False
    
    def test_basic_operations(self):
        """测试基本数据库操作"""
        print("\n🔧 测试基本数据库操作...")
        
        try:
            # 1. 测试添加RSSHub端点
            success = self.handler.add_rsshub_endpoint("https://rsshub.app")
            if success:
                print("  ✅ 添加RSSHub端点成功")
            else:
                print("  ❌ 添加RSSHub端点失败")
                return False
            
            # 2. 测试添加RSS源
            feed_id = self.handler.add_or_update_feed(
                "https://example.com/rss", 
                "测试RSS频道", 
                "这是一个测试频道"
            )
            if feed_id > 0:
                print(f"  ✅ 添加RSS源成功，ID: {feed_id}")
            else:
                print("  ❌ 添加RSS源失败")
                return False
            
            # 3. 测试添加订阅
            user_id = "test_platform:group:123456"
            success = self.handler.add_subscription(user_id, feed_id, "0 * * * *")
            if success:
                print(f"  ✅ 添加订阅成功，用户: {user_id}")
            else:
                print("  ❌ 添加订阅失败")
                return False
            
            # 4. 测试查询功能
            subscriptions = self.handler.get_user_subscriptions(user_id)
            if len(subscriptions) == 1:
                print(f"  ✅ 查询用户订阅成功，数量: {len(subscriptions)}")
            else:
                print(f"  ❌ 查询用户订阅失败，期望1个，实际{len(subscriptions)}个")
                return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 基本操作测试失败: {e}")
            return False
    
    def test_rss_content_parsing(self):
        """测试RSS内容解析和存储"""
        print("\n📰 测试RSS内容解析和存储...")
        
        try:
            # 生成测试RSS内容
            rss_xml = MockRSSServer.generate_rss_xml("测试新闻频道", 5)
            
            # 解析RSS内容
            root = etree.fromstring(rss_xml.encode('utf-8'))
            items = root.xpath("//item")
            
            print(f"  📄 解析到 {len(items)} 个RSS条目")
            
            # 获取RSS源ID
            feed_id = self.handler.add_or_update_feed(
                "https://test.example.com/rss",
                "测试新闻频道",
                "测试RSS解析功能"
            )
            
            # 保存RSS条目到数据库
            saved_count = 0
            for item in items:
                try:
                    title = item.xpath("title")[0].text
                    link = item.xpath("link")[0].text
                    description = item.xpath("description")[0].text if item.xpath("description") else ""
                    pub_date = item.xpath("pubDate")[0].text if item.xpath("pubDate") else ""
                    
                    # 解析发布时间
                    pub_date_timestamp = 0
                    if pub_date:
                        try:
                            pub_date_parsed = time.strptime(
                                pub_date.replace("GMT", "+0000"),
                                "%a, %d %b %Y %H:%M:%S %z"
                            )
                            pub_date_timestamp = int(time.mktime(pub_date_parsed))
                        except:
                            pub_date_timestamp = int(time.time())
                    
                    # 提取图片URL
                    pic_urls = []
                    if "img src=" in description:
                        import re
                        img_matches = re.findall(r'&lt;img src="([^"]+)"&gt;', description)
                        pic_urls.extend(img_matches)
                    
                    # 保存到数据库
                    item_id = self.handler.save_rss_item(
                        feed_id, title, link, description,
                        pub_date, pub_date_timestamp, pic_urls
                    )
                    
                    if item_id is not None:
                        saved_count += 1
                        print(f"    ✅ 保存条目: {title[:30]}...")
                    
                except Exception as e:
                    print(f"    ❌ 解析条目失败: {e}")
            
            print(f"  📊 成功保存 {saved_count} 个RSS条目")
            
            # 测试重复保存（应该被去重）
            duplicate_count = 0
            for item in items[:2]:  # 重复保存前2个
                title = item.xpath("title")[0].text
                link = item.xpath("link")[0].text
                description = item.xpath("description")[0].text if item.xpath("description") else ""
                
                item_id = self.handler.save_rss_item(
                    feed_id, title, link, description
                )
                
                if item_id is None:
                    duplicate_count += 1
            
            print(f"  🔄 去重测试: {duplicate_count} 个重复条目被正确拒绝")
            
            return saved_count > 0
            
        except Exception as e:
            print(f"  ❌ RSS内容解析测试失败: {e}")
            return False
    
    def test_push_logic(self):
        """测试推送逻辑"""
        print("\n📤 测试推送逻辑...")
        
        try:
            user_id = "test_platform:group:123456"
            
            # 获取RSS源
            feed = self.handler.get_feed_by_url("https://test.example.com/rss")
            if not feed:
                print("  ❌ 未找到测试RSS源")
                return False
            
            feed_id = feed['id']
            
            # 添加订阅（如果不存在）
            self.handler.add_subscription(user_id, feed_id, "0 * * * *")
            
            # 获取用户未推送的新条目
            new_items = self.handler.get_new_items_for_user(user_id, feed_id, 0)
            print(f"  📋 找到 {len(new_items)} 个未推送条目")
            
            if len(new_items) == 0:
                print("  ⚠️ 没有可推送的内容")
                return True
            
            # 模拟推送过程
            pushed_count = 0
            for item in new_items[:3]:  # 只推送前3个
                print(f"    📨 推送: {item['title'][:40]}...")
                print(f"       链接: {item['link']}")
                print(f"       时间: {item['pub_date']}")
                if item['pic_urls']:
                    print(f"       图片: {len(item['pic_urls'])} 张")
                
                # 标记为已推送
                success = self.handler.mark_item_as_pushed(user_id, item['id'])
                if success:
                    pushed_count += 1
                    print(f"    ✅ 标记推送成功")
                else:
                    print(f"    ❌ 标记推送失败")
            
            print(f"  📊 成功推送 {pushed_count} 个条目")
            
            # 验证推送状态
            remaining_items = self.handler.get_new_items_for_user(user_id, feed_id, 0)
            print(f"  🔍 推送后剩余未推送条目: {len(remaining_items)} 个")
            
            # 测试重复推送检查
            if new_items:
                is_pushed = self.handler.is_item_pushed_to_user(user_id, new_items[0]['id'])
                if is_pushed:
                    print("  ✅ 重复推送检查正常")
                else:
                    print("  ❌ 重复推送检查失败")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 推送逻辑测试失败: {e}")
            return False
    
    def test_multi_user_scenario(self):
        """测试多用户场景"""
        print("\n👥 测试多用户场景...")
        
        try:
            # 创建多个用户
            users = [
                "test_platform:group:111111",
                "test_platform:group:222222", 
                "test_platform:private:333333"
            ]
            
            # 获取RSS源
            feed = self.handler.get_feed_by_url("https://test.example.com/rss")
            feed_id = feed['id']
            
            # 为每个用户添加订阅
            for user_id in users:
                success = self.handler.add_subscription(user_id, feed_id, "0 * * * *")
                if success:
                    print(f"  ✅ 用户 {user_id.split(':')[-1]} 订阅成功")
            
            # 为第一个用户推送一些内容
            user1_items = self.handler.get_new_items_for_user(users[0], feed_id, 0)
            for item in user1_items[:2]:
                self.handler.mark_item_as_pushed(users[0], item['id'])
            print(f"  📤 用户1推送了 2 个条目")
            
            # 检查各用户的未推送内容
            for i, user_id in enumerate(users):
                new_items = self.handler.get_new_items_for_user(user_id, feed_id, 0)
                print(f"  📊 用户{i+1} 未推送条目: {len(new_items)} 个")
            
            # 验证推送独立性
            user1_new = self.handler.get_new_items_for_user(users[0], feed_id, 0)
            user2_new = self.handler.get_new_items_for_user(users[1], feed_id, 0)
            
            if len(user2_new) > len(user1_new):
                print("  ✅ 多用户推送状态独立性验证成功")
            else:
                print("  ❌ 多用户推送状态独立性验证失败")
                return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 多用户场景测试失败: {e}")
            return False
    
    def test_database_queries(self):
        """测试数据库查询功能"""
        print("\n🔍 测试数据库查询功能...")
        
        try:
            # 查询统计信息
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 统计各表数据量
                tables = ['rsshub_endpoints', 'rss_feeds', 'subscriptions', 'rss_items', 'push_records']
                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    count = cursor.fetchone()[0]
                    print(f"  📊 {table}: {count} 条记录")
                
                # 查询最新的RSS条目
                cursor.execute('''
                    SELECT f.title as feed_title, i.title, i.pub_date_timestamp
                    FROM rss_items i
                    JOIN rss_feeds f ON i.feed_id = f.id
                    ORDER BY i.pub_date_timestamp DESC
                    LIMIT 3
                ''')
                
                latest_items = cursor.fetchall()
                print(f"  📰 最新条目:")
                for item in latest_items:
                    print(f"    - [{item[0]}] {item[1][:40]}...")
                
                # 查询推送统计
                cursor.execute('''
                    SELECT user_id, COUNT(*) as push_count
                    FROM push_records
                    GROUP BY user_id
                ''')
                
                push_stats = cursor.fetchall()
                print(f"  📤 推送统计:")
                for stat in push_stats:
                    user_short = stat[0].split(':')[-1]
                    print(f"    - 用户 {user_short}: {stat[1]} 条推送")
                
                return True
                
        except Exception as e:
            print(f"  ❌ 数据库查询测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始RSS插件数据库功能完整测试")
        print("=" * 60)
        
        self.setup()
        
        tests = [
            ("数据库结构", self.test_database_structure),
            ("基本操作", self.test_basic_operations),
            ("RSS解析", self.test_rss_content_parsing),
            ("推送逻辑", self.test_push_logic),
            ("多用户场景", self.test_multi_user_scenario),
            ("数据库查询", self.test_database_queries),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    print(f"\n✅ {test_name} 测试通过")
                    passed += 1
                else:
                    print(f"\n❌ {test_name} 测试失败")
            except Exception as e:
                print(f"\n❌ {test_name} 测试异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"🎯 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！RSS插件数据库功能正常")
            success = True
        else:
            print("⚠️ 部分测试失败，请检查相关功能")
            success = False
        
        # 显示数据库文件信息
        if os.path.exists(self.db_path):
            size = os.path.getsize(self.db_path)
            print(f"📁 测试数据库大小: {size} 字节")
            print(f"📍 数据库位置: {os.path.abspath(self.db_path)}")
        
        return success

def main():
    """主函数"""
    test_suite = RSSTestSuite()
    
    try:
        success = test_suite.run_all_tests()
        
        # 询问是否保留测试数据库
        print(f"\n❓ 是否保留测试数据库 '{test_suite.db_path}' 用于查看？")
        print("   (输入 'y' 保留，其他任意键删除)")
        
        try:
            choice = input().strip().lower()
            if choice != 'y':
                test_suite.cleanup()
            else:
                print(f"📁 测试数据库已保留: {os.path.abspath(test_suite.db_path)}")
                print("   可以使用 sqlite3 命令查看内容:")
                print(f"   sqlite3 {test_suite.db_path}")
        except:
            test_suite.cleanup()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        test_suite.cleanup()
        return 1
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        test_suite.cleanup()
        return 1

if __name__ == "__main__":
    sys.exit(main())
