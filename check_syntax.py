#!/usr/bin/env python3
"""
检查Python文件语法的脚本
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件的语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source, filename=filename)
        print(f"✅ {filename} 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ {filename} 检查失败: {e}")
        return False

def main():
    """主函数"""
    files_to_check = [
        'main.py',
        'data_handler.py', 
        'rss.py',
        'pic_handler.py'
    ]
    
    print("检查Python文件语法...")
    print("=" * 40)
    
    all_good = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_good = False
    
    print("=" * 40)
    if all_good:
        print("✅ 所有文件语法检查通过！")
        return 0
    else:
        print("❌ 部分文件存在语法错误")
        return 1

if __name__ == "__main__":
    sys.exit(main())
