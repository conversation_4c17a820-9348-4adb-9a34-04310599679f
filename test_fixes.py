#!/usr/bin/env python3
"""
测试RSS插件修复的脚本
"""

import json
import sys
import os

def test_config_schema():
    """测试配置文件是否正确"""
    try:
        with open('_conf_schema.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查是否添加了merge_forward配置
        if 'merge_forward' not in config:
            print("❌ 配置文件中缺少 merge_forward 选项")
            return False
        
        merge_forward_config = config['merge_forward']
        
        # 检查配置项的基本属性
        required_fields = ['description', 'type', 'hint', 'default']
        for field in required_fields:
            if field not in merge_forward_config:
                print(f"❌ merge_forward 配置缺少 {field} 字段")
                return False
        
        # 检查类型是否正确
        if merge_forward_config['type'] != 'bool':
            print("❌ merge_forward 配置类型应该是 bool")
            return False
        
        # 检查默认值是否正确
        if merge_forward_config['default'] != False:
            print("❌ merge_forward 配置默认值应该是 false")
            return False
        
        print("✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except FileNotFoundError:
        print("❌ 配置文件不存在")
        return False
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def test_main_py_syntax():
    """测试main.py语法是否正确"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否添加了merge_forward配置读取
        if 'self.merge_forward = config.get("merge_forward")' not in content:
            print("❌ main.py中缺少merge_forward配置读取")
            return False
        
        # 检查是否添加了清理方法
        if 'def __del__(self):' not in content:
            print("❌ main.py中缺少__del__方法")
            return False
        
        if 'def cleanup(self):' not in content:
            print("❌ main.py中缺少cleanup方法")
            return False
        
        # 检查是否修改了消息发送逻辑
        if 'if self.merge_forward and platform_name == "aiocqhttp":' not in content:
            print("❌ main.py中缺少合并转发逻辑")
            return False
        
        # 检查是否修复了rsshub_remove方法
        if 'self.scheduler.remove_job()' in content:
            print("❌ main.py中仍有不完整的remove_job调用")
            return False
        
        print("✅ main.py语法检查通过")
        return True
        
    except FileNotFoundError:
        print("❌ main.py文件不存在")
        return False
    except Exception as e:
        print(f"❌ main.py检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试RSS插件修复...")
    print("=" * 50)
    
    config_ok = test_config_schema()
    main_ok = test_main_py_syntax()
    
    print("=" * 50)
    if config_ok and main_ok:
        print("✅ 所有测试通过！修复成功。")
        print("\n修复内容总结:")
        print("1. ✅ 添加了插件停止时的清理机制(__del__和cleanup方法)")
        print("2. ✅ 在配置文件中添加了merge_forward选项")
        print("3. ✅ 实现了根据配置选择是否使用合并转发的功能")
        print("4. ✅ 修复了rsshub_remove方法中的不完整代码")
        return 0
    else:
        print("❌ 测试失败，请检查修复内容。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
