#!/usr/bin/env python3
"""
测试模块导入的脚本
"""

import sys
import os

def test_data_handler_import():
    """测试data_handler模块导入"""
    try:
        from data_handler import DataHandler
        print("✅ data_handler 导入成功")
        
        # 测试基本初始化
        handler = DataHandler("test_import.db")
        print("✅ DataHandler 初始化成功")
        
        # 清理测试文件
        if os.path.exists("test_import.db"):
            os.remove("test_import.db")
        
        return True
    except Exception as e:
        print(f"❌ data_handler 导入失败: {e}")
        return False

def test_rss_import():
    """测试rss模块导入"""
    try:
        from rss import RSSItem
        print("✅ rss 模块导入成功")
        
        # 测试RSSItem创建
        item = RSSItem(
            chan_title="测试频道",
            title="测试标题", 
            link="https://example.com",
            description="测试描述",
            pubDate="",
            pubDate_timestamp=0,
            pic_urls=[]
        )
        print("✅ RSSItem 创建成功")
        return True
    except Exception as e:
        print(f"❌ rss 模块导入失败: {e}")
        return False

def test_pic_handler_import():
    """测试pic_handler模块导入"""
    try:
        from pic_handler import RssImageHandler
        print("✅ pic_handler 导入成功")
        
        # 测试初始化
        handler = RssImageHandler()
        print("✅ RssImageHandler 初始化成功")
        return True
    except Exception as e:
        print(f"❌ pic_handler 导入失败: {e}")
        return False

def test_typing_imports():
    """测试typing模块导入"""
    try:
        from typing import List, Dict, Optional, Tuple
        print("✅ typing 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ typing 模块导入失败: {e}")
        return False

def test_main_imports():
    """测试main.py中的导入"""
    try:
        # 测试基本导入
        import time
        import re
        import logging
        from typing import List, Dict
        print("✅ 基本模块导入成功")
        
        # 测试可能的问题导入
        try:
            from lxml import etree
            print("✅ lxml 导入成功")
        except ImportError:
            print("⚠️ lxml 未安装，但这是可选的")
        
        try:
            from apscheduler.schedulers.asyncio import AsyncIOScheduler
            print("✅ apscheduler 导入成功")
        except ImportError:
            print("⚠️ apscheduler 未安装，但这是可选的")
        
        try:
            import aiohttp
            print("✅ aiohttp 导入成功")
        except ImportError:
            print("⚠️ aiohttp 未安装，但这是可选的")
        
        return True
    except Exception as e:
        print(f"❌ main.py 导入测试失败: {e}")
        return False

def test_sqlite_import():
    """测试SQLite导入"""
    try:
        import sqlite3
        print("✅ sqlite3 导入成功")
        
        # 测试基本操作
        conn = sqlite3.connect(":memory:")
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY)")
        cursor.execute("INSERT INTO test (id) VALUES (1)")
        cursor.execute("SELECT COUNT(*) FROM test")
        count = cursor.fetchone()[0]
        conn.close()
        
        assert count == 1, "SQLite基本操作失败"
        print("✅ SQLite 基本操作正常")
        return True
    except Exception as e:
        print(f"❌ SQLite 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试模块导入...")
    print("=" * 50)
    
    tests = [
        test_typing_imports,
        test_sqlite_import,
        test_data_handler_import,
        test_rss_import,
        test_pic_handler_import,
        test_main_imports,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        print("-" * 30)
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有导入测试通过！")
        return 0
    else:
        print("❌ 部分导入测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
