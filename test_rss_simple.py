#!/usr/bin/env python3
"""
简化的RSS插件数据库功能测试
不依赖外部库，专注测试核心数据库逻辑
"""

import os
import sys
import sqlite3
import json
import time
import hashlib

class SimpleDataHandler:
    """简化的数据处理器，用于测试"""
    
    def __init__(self, db_path="test_simple_rss.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rss_feeds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    title TEXT,
                    description TEXT
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    feed_id INTEGER NOT NULL,
                    cron_expr TEXT NOT NULL,
                    last_check_time INTEGER DEFAULT 0,
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id),
                    UNIQUE(user_id, feed_id)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rss_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    feed_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    link TEXT NOT NULL,
                    description TEXT,
                    pub_date_timestamp INTEGER,
                    content_hash TEXT UNIQUE NOT NULL,
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS push_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    item_id INTEGER NOT NULL,
                    pushed_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (item_id) REFERENCES rss_items (id),
                    UNIQUE(user_id, item_id)
                )
            ''')
            
            conn.commit()
    
    def add_feed(self, url, title, description):
        """添加RSS源"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('INSERT OR REPLACE INTO rss_feeds (url, title, description) VALUES (?, ?, ?)',
                         (url, title, description))
            return cursor.lastrowid
    
    def add_subscription(self, user_id, feed_id, cron_expr):
        """添加订阅"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('INSERT OR REPLACE INTO subscriptions (user_id, feed_id, cron_expr, last_check_time) VALUES (?, ?, ?, ?)',
                         (user_id, feed_id, cron_expr, int(time.time())))
            return cursor.rowcount > 0
    
    def save_rss_item(self, feed_id, title, link, description, pub_timestamp):
        """保存RSS条目"""
        content_hash = hashlib.md5(f"{title}|{link}|{description}".encode()).hexdigest()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            try:
                cursor.execute('''
                    INSERT INTO rss_items (feed_id, title, link, description, pub_date_timestamp, content_hash)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (feed_id, title, link, description, pub_timestamp, content_hash))
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                return None  # 重复内容
    
    def get_new_items_for_user(self, user_id, feed_id, after_timestamp=0):
        """获取用户未推送的新条目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT i.id, i.title, i.link, i.description, i.pub_date_timestamp
                FROM rss_items i
                LEFT JOIN push_records p ON i.id = p.item_id AND p.user_id = ?
                WHERE i.feed_id = ? AND i.pub_date_timestamp > ? AND p.id IS NULL
                ORDER BY i.pub_date_timestamp DESC
            ''', (user_id, feed_id, after_timestamp))
            
            return [{'id': row[0], 'title': row[1], 'link': row[2], 
                    'description': row[3], 'pub_date_timestamp': row[4]} 
                   for row in cursor.fetchall()]
    
    def mark_item_as_pushed(self, user_id, item_id):
        """标记条目已推送"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('INSERT OR IGNORE INTO push_records (user_id, item_id) VALUES (?, ?)',
                         (user_id, item_id))
            return cursor.rowcount > 0

def test_rss_database_functionality():
    """测试RSS数据库功能"""
    print("🚀 开始RSS数据库功能测试")
    print("=" * 50)
    
    db_path = "test_rss_functionality.db"
    
    # 清理旧测试文件
    if os.path.exists(db_path):
        os.remove(db_path)
    
    try:
        handler = SimpleDataHandler(db_path)
        
        # 测试1: 添加RSS源
        print("\n📰 测试1: 添加RSS源")
        feed_id = handler.add_feed(
            "https://example.com/rss",
            "测试新闻频道", 
            "这是一个测试RSS频道"
        )
        print(f"✅ RSS源添加成功，ID: {feed_id}")
        
        # 测试2: 添加用户订阅
        print("\n👤 测试2: 添加用户订阅")
        users = [
            "test_platform:group:111111",
            "test_platform:group:222222",
            "test_platform:private:333333"
        ]
        
        for user_id in users:
            success = handler.add_subscription(user_id, feed_id, "0 * * * *")
            if success:
                print(f"✅ 用户 {user_id.split(':')[-1]} 订阅成功")
        
        # 测试3: 添加RSS内容
        print("\n📄 测试3: 添加RSS内容")
        current_time = int(time.time())
        
        test_items = [
            {
                "title": "重要新闻：测试标题1",
                "link": "https://example.com/news/1",
                "description": "这是第一条测试新闻的详细描述内容",
                "timestamp": current_time - 3600
            },
            {
                "title": "科技资讯：测试标题2", 
                "link": "https://example.com/news/2",
                "description": "这是第二条测试新闻，关于科技发展",
                "timestamp": current_time - 1800
            },
            {
                "title": "体育新闻：测试标题3",
                "link": "https://example.com/news/3", 
                "description": "这是第三条测试新闻，体育赛事报道",
                "timestamp": current_time - 900
            },
            {
                "title": "重要新闻：测试标题1",  # 重复内容测试
                "link": "https://example.com/news/1",
                "description": "这是第一条测试新闻的详细描述内容",
                "timestamp": current_time - 600
            }
        ]
        
        saved_count = 0
        duplicate_count = 0
        
        for item in test_items:
            item_id = handler.save_rss_item(
                feed_id,
                item["title"],
                item["link"], 
                item["description"],
                item["timestamp"]
            )
            
            if item_id:
                saved_count += 1
                print(f"✅ 保存: {item['title'][:30]}...")
            else:
                duplicate_count += 1
                print(f"🔄 去重: {item['title'][:30]}... (重复内容)")
        
        print(f"📊 保存了 {saved_count} 条新内容，去重了 {duplicate_count} 条重复内容")
        
        # 测试4: 获取未推送内容
        print("\n📤 测试4: 获取未推送内容")
        for user_id in users:
            new_items = handler.get_new_items_for_user(user_id, feed_id, 0)
            print(f"👤 用户 {user_id.split(':')[-1]}: {len(new_items)} 条未推送内容")
            
            for item in new_items:
                print(f"   - {item['title'][:40]}...")
        
        # 测试5: 模拟推送过程
        print("\n📨 测试5: 模拟推送过程")
        
        # 为第一个用户推送前2条内容
        user1_items = handler.get_new_items_for_user(users[0], feed_id, 0)
        for item in user1_items[:2]:
            success = handler.mark_item_as_pushed(users[0], item['id'])
            if success:
                print(f"✅ 推送给用户1: {item['title'][:30]}...")
        
        # 为第二个用户推送第1条内容
        user2_items = handler.get_new_items_for_user(users[1], feed_id, 0)
        if user2_items:
            success = handler.mark_item_as_pushed(users[1], user2_items[0]['id'])
            if success:
                print(f"✅ 推送给用户2: {user2_items[0]['title'][:30]}...")
        
        # 测试6: 验证推送状态独立性
        print("\n🔍 测试6: 验证推送状态独立性")
        for i, user_id in enumerate(users):
            remaining_items = handler.get_new_items_for_user(user_id, feed_id, 0)
            print(f"👤 用户{i+1} 剩余未推送: {len(remaining_items)} 条")
        
        # 测试7: 数据库统计
        print("\n📊 测试7: 数据库统计")
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 统计各表数据
            tables = ['rss_feeds', 'subscriptions', 'rss_items', 'push_records']
            for table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f"📋 {table}: {count} 条记录")
            
            # 查询推送统计
            cursor.execute('''
                SELECT user_id, COUNT(*) as push_count
                FROM push_records 
                GROUP BY user_id
            ''')
            
            push_stats = cursor.fetchall()
            print(f"📤 推送统计:")
            for stat in push_stats:
                user_short = stat[0].split(':')[-1]
                print(f"   用户 {user_short}: {stat[1]} 条推送")
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！RSS数据库功能正常")
        
        # 显示数据库信息
        size = os.path.getsize(db_path)
        print(f"📁 数据库大小: {size} 字节")
        print(f"📍 数据库位置: {os.path.abspath(db_path)}")
        
        # 询问是否保留数据库
        print(f"\n❓ 是否保留测试数据库用于查看？(y/n)")
        try:
            choice = input().strip().lower()
            if choice != 'y':
                os.remove(db_path)
                print("🗑️ 测试数据库已删除")
            else:
                print(f"📁 数据库已保留，可用以下命令查看:")
                print(f"   sqlite3 {db_path}")
                print(f"   .tables")
                print(f"   SELECT * FROM rss_items;")
        except:
            os.remove(db_path)
            print("🗑️ 测试数据库已删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理
        if os.path.exists(db_path):
            os.remove(db_path)
        
        return False

def test_existing_database():
    """测试现有的数据库文件"""
    db_file = "astrbot_plugin_rss.db"
    
    if not os.path.exists(db_file):
        print(f"❌ 数据库文件 {db_file} 不存在")
        return False
    
    print(f"🔍 分析现有数据库: {db_file}")
    print("=" * 50)
    
    try:
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 数据库表: {', '.join(tables)}")
            
            # 统计数据
            for table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f"📊 {table}: {count} 条记录")
            
            # 如果有RSS条目，显示最新的几条
            if 'rss_items' in tables:
                cursor.execute('''
                    SELECT title, link, pub_date_timestamp 
                    FROM rss_items 
                    ORDER BY pub_date_timestamp DESC 
                    LIMIT 5
                ''')
                items = cursor.fetchall()
                if items:
                    print(f"\n📰 最新RSS条目:")
                    for item in items:
                        timestamp_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(item[2])) if item[2] else '未知时间'
                        print(f"   - {item[0][:50]}... ({timestamp_str})")
            
            # 如果有推送记录，显示统计
            if 'push_records' in tables:
                cursor.execute('''
                    SELECT user_id, COUNT(*) as push_count
                    FROM push_records 
                    GROUP BY user_id
                ''')
                push_stats = cursor.fetchall()
                if push_stats:
                    print(f"\n📤 推送统计:")
                    for stat in push_stats:
                        print(f"   用户 {stat[0]}: {stat[1]} 条推送")
        
        print("\n✅ 数据库分析完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库分析失败: {e}")
        return False

def main():
    """主函数"""
    print("RSS插件数据库测试工具")
    print("=" * 30)
    
    # 首先检查是否有现有数据库
    if os.path.exists("astrbot_plugin_rss.db"):
        print("🔍 发现现有数据库文件")
        test_existing_database()
        print("\n" + "=" * 30)
    
    # 运行功能测试
    print("🧪 运行功能测试")
    success = test_rss_database_functionality()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
