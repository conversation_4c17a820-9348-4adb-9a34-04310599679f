import aiohttp
import time
import re
import logging
from lxml import etree
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from astrbot.api.event import filter, AstrMessageEvent, MessageEventResult,MessageChain
from astrbot.api.star import Context, Star, register
from astrbot.api import AstrBotConfig
import astrbot.api.message_components as Comp

from .data_handler import DataHandler
from .pic_handler import RssImageHandler
from .rss import RSSItem
from typing import List, Dict


@register(
    "astrbot_plugin_rss",
    "Soulter",
    "RSS订阅插件",
    "1.1.0",
    "https://github.com/Soulter/astrbot_plugin_rss",
)
class RssPlugin(Star):
    def __init__(self, context: Context, config: AstrBotConfig) -> None:
        super().__init__(context)

        self.logger = logging.getLogger("astrbot")
        self.context = context
        self.config = config
        self.data_handler = DataHandler()
        self.pic_handler = RssImageHandler()

        # 尝试从旧数据迁移
        self.data_handler.migrate_from_json()

        # 定期清理旧的推送记录
        self.data_handler.cleanup_old_push_records()

        # 提取scheme文件中的配置
        self.title_max_length = config.get("title_max_length")
        self.description_max_length = config.get("description_max_length")
        self.max_items_per_poll = config.get("max_items_per_poll")
        self.t2i = config.get("t2i")
        self.is_hide_url = config.get("is_hide_url")
        self.merge_forward = config.get("merge_forward")
        self.is_read_pic= config.get("pic_config").get("is_read_pic")
        self.is_adjust_pic= config.get("pic_config").get("is_adjust_pic")
        self.max_pic_item = config.get("pic_config").get("max_pic_item")

        self.pic_handler = RssImageHandler(self.is_adjust_pic)
        self.scheduler = AsyncIOScheduler()
        self.scheduler.start()

        self._fresh_asyncIOScheduler()

    def __del__(self):
        """插件析构时清理资源"""
        try:
            if hasattr(self, 'scheduler') and self.scheduler:
                self.logger.info("RSS插件停止，清理定时任务")
                self.scheduler.remove_all_jobs()
                if self.scheduler.running:
                    self.scheduler.shutdown(wait=False)
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"RSS插件清理资源时出错: {str(e)}")

    def cleanup(self):
        """手动清理方法，可以被外部调用"""
        try:
            if hasattr(self, 'scheduler') and self.scheduler:
                self.logger.info("RSS插件手动清理，停止定时任务")
                self.scheduler.remove_all_jobs()
                if self.scheduler.running:
                    self.scheduler.shutdown(wait=False)
        except Exception as e:
            self.logger.error(f"RSS插件手动清理时出错: {str(e)}")

    def parse_cron_expr(self, cron_expr: str):
        fields = cron_expr.split(" ")
        return {
            "minute": fields[0],
            "hour": fields[1],
            "day": fields[2],
            "month": fields[3],
            "day_of_week": fields[4],
        }

    async def parse_channel_info(self, url):
        async with aiohttp.ClientSession(trust_env=True) as session:
            async with session.get(url) as resp:
                if resp.status != 200:
                    return None
                text = await resp.read()
                return self.data_handler.parse_channel_info(text)

    async def cron_task_callback(self, user_id: str, feed_id: int):
        """定时任务回调"""

        # 获取订阅信息
        subscriptions = self.data_handler.get_user_subscriptions(user_id)
        subscription = None
        for sub in subscriptions:
            if sub['feed_id'] == feed_id:
                subscription = sub
                break

        if not subscription:
            self.logger.warning(f"未找到订阅信息: user_id={user_id}, feed_id={feed_id}")
            return

        self.logger.info(f"RSS 定时任务触发: {subscription['url']} - {user_id}")

        # 拉取RSS并保存到数据库
        await self.poll_and_save_rss(subscription['url'], feed_id)

        # 获取用户未推送的新条目
        last_check_time = subscription['last_check_time']
        new_items = self.data_handler.get_new_items_for_user(user_id, feed_id, last_check_time)

        if not new_items:
            self.logger.info(f"RSS 定时任务 {subscription['url']} 无新内容 - {user_id}")
            return

        # 限制推送数量
        max_items_per_poll = self.max_items_per_poll
        if max_items_per_poll > 0:
            new_items = new_items[:max_items_per_poll]

        # 分解MessageSession
        platform_name, message_type, session_id = user_id.split(":")

        # 根据配置决定是否使用合并转发
        if self.merge_forward and platform_name == "aiocqhttp":
            # 使用合并转发（仅支持aiocqhttp平台）
            nodes = []
            for item in new_items:
                rss_item = self._convert_db_item_to_rss_item(item)
                comps = await self._get_chain_components(rss_item)
                node = Comp.Node(
                    uin=0,
                    name="Astrbot",
                    content=comps
                )
                nodes.append(node)
                # 标记为已推送
                self.data_handler.mark_item_as_pushed(user_id, item['id'])

            # 合并消息发送
            if len(nodes) > 0:
                msc = MessageChain(
                    chain=nodes,
                    use_t2i_=self.t2i
                )
                await self.context.send_message(user_id, msc)
        else:
            # 每个消息单独发送
            for item in new_items:
                rss_item = self._convert_db_item_to_rss_item(item)
                comps = await self._get_chain_components(rss_item)
                msc = MessageChain(
                    chain=comps,
                    use_t2i_=self.t2i
                )
                await self.context.send_message(user_id, msc)
                # 标记为已推送
                self.data_handler.mark_item_as_pushed(user_id, item['id'])

        # 更新最后检查时间
        self.data_handler.update_subscription_check_time(user_id, feed_id)
        self.logger.info(f"RSS 定时任务 {subscription['url']} 推送成功，推送了 {len(new_items)} 条内容 - {user_id}")

    def _convert_db_item_to_rss_item(self, db_item: Dict):
        """将数据库条目转换为RSSItem对象"""
        from .rss import RSSItem
        return RSSItem(
            chan_title=db_item['feed_title'],
            title=db_item['title'],
            link=db_item['link'],
            description=db_item['description'],
            pubDate=db_item['pub_date'],
            pubDate_timestamp=db_item['pub_date_timestamp'],
            pic_urls=db_item['pic_urls']
        )

    async def poll_and_save_rss(self, url: str, feed_id: int) -> int:
        """拉取RSS并保存到数据库，返回新增条目数"""
        try:
            async with aiohttp.ClientSession(trust_env=True) as session:
                async with session.get(url) as resp:
                    if resp.status != 200:
                        self.logger.error(f"rss: 无法正常打开站点 {url}")
                        return 0
                    text = await resp.read()
                    root = etree.fromstring(text)
                    items = root.xpath("//item")

                    new_count = 0
                    for item in items:
                        try:
                            title = item.xpath("title")[0].text
                            if len(title) > self.title_max_length:
                                title = title[:self.title_max_length] + "..."

                            link = item.xpath("link")[0].text
                            if not re.match(r"^https?://", link):
                                link = self.data_handler.get_root_url(url) + link

                            description = item.xpath("description")[0].text if item.xpath("description") else ""
                            pic_url_list = self.data_handler.strip_html_pic(description)
                            description = self.data_handler.strip_html(description)

                            if len(description) > self.description_max_length:
                                description = description[:self.description_max_length] + "..."

                            pub_date = ""
                            pub_date_timestamp = 0
                            if item.xpath("pubDate"):
                                pub_date = item.xpath("pubDate")[0].text
                                try:
                                    pub_date_parsed = time.strptime(
                                        pub_date.replace("GMT", "+0000"),
                                        "%a, %d %b %Y %H:%M:%S %z",
                                    )
                                    pub_date_timestamp = int(time.mktime(pub_date_parsed))
                                except:
                                    pub_date_timestamp = int(time.time())

                            # 保存到数据库
                            item_id = self.data_handler.save_rss_item(
                                feed_id, title, link, description,
                                pub_date, pub_date_timestamp, pic_url_list
                            )

                            if item_id is not None:
                                new_count += 1

                        except Exception as e:
                            self.logger.error(f"rss: 解析RSS条目 {url} 失败: {str(e)}")
                            continue

                    return new_count

        except Exception as e:
            self.logger.error(f"rss: 拉取RSS {url} 失败: {str(e)}")
            return 0



    def _fresh_asyncIOScheduler(self):
        """刷新定时任务"""
        # 删除所有定时任务
        self.logger.info("刷新定时任务")
        self.scheduler.remove_all_jobs()

        # 为每个订阅添加定时任务
        subscriptions = self.data_handler.get_all_subscriptions()
        for sub in subscriptions:
            job_id = f"rss_{sub['user_id']}_{sub['feed_id']}"
            self.scheduler.add_job(
                self.cron_task_callback,
                "cron",
                **self.parse_cron_expr(sub["cron_expr"]),
                args=[sub['user_id'], sub['feed_id']],
                id=job_id,
                replace_existing=True
            )

        self.logger.info(f"已添加 {len(subscriptions)} 个定时任务")

    async def _add_url(self, url: str, cron_expr: str, message: AstrMessageEvent):
        """内部方法：添加URL订阅的共用逻辑"""
        user_id = message.unified_msg_origin

        try:
            # 检查RSS源是否已存在
            feed = self.data_handler.get_feed_by_url(url)
            if feed:
                feed_id = feed['id']
            else:
                # 解析频道信息
                title, desc = await self.parse_channel_info(url)
                # 添加新的RSS源
                feed_id = self.data_handler.add_or_update_feed(url, title, desc)
                feed = {'title': title, 'description': desc}

            # 添加订阅
            success = self.data_handler.add_subscription(user_id, feed_id, cron_expr)
            if not success:
                return message.plain_result("添加订阅失败")

            # 拉取最新内容并保存到数据库
            await self.poll_and_save_rss(url, feed_id)

            return {
                'title': feed.get('title', '未知频道'),
                'description': feed.get('description', '无描述')
            }

        except Exception as e:
            self.logger.error(f"添加URL订阅失败: {e}")
            return message.plain_result(f"解析频道信息失败: {str(e)}")

    async def _get_chain_components(self, item: RSSItem):
        """组装消息链"""
        comps = []
        comps.append(Comp.Plain(f"频道 {item.chan_title} 最新 Feed\n---\n标题: {item.title}\n---\n"))
        if not self.is_hide_url:
            comps.append(Comp.Plain(f"链接: {item.link}\n---\n"))
        comps.append(Comp.Plain(item.description+"\n---\n"))
        if self.is_read_pic and item.pic_urls:
            # 如果max_pic_item为-1则不限制图片数量
            temp_max_pic_item = len(item.pic_urls) if self.max_pic_item == -1 else self.max_pic_item
            for pic_url in item.pic_urls[:temp_max_pic_item]:
                base64str = await self.pic_handler.modify_corner_pixel_to_base64(pic_url)
                if base64str is None:
                    comps.append(Comp.Plain("图片链接读取失败\n"))
                    continue
                else:
                    comps.append(Comp.Image.fromBase64(base64str))
        return comps


    def _is_url_or_ip(self,text: str) -> bool:
        """
        判断一个字符串是否为网址（http/https 开头）或 IP 地址。
        """
        url_pattern = r"^(?:http|https)://.+$"
        ip_pattern = r"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        return bool(re.match(url_pattern, text) or re.match(ip_pattern, text))

    @filter.command_group("rss", alias={"RSS"})
    def rss(self):
        """RSS订阅插件

        可以订阅和管理多个RSS源，支持cron表达式设置更新频率

        cron 表达式格式：
        * * * * *，分别表示分钟 小时 日 月 星期，* 表示任意值，支持范围和逗号分隔。例：
        1. 0 0 * * * 表示每天 0 点触发。
        2. 0/5 * * * * 表示每 5 分钟触发。
        3. 0 9-18 * * * 表示每天 9 点到 18 点触发。
        4. 0 0 1,15 * * 表示每月 1 号和 15 号 0 点触发。
        星期的取值范围是 0-6，0 表示星期天。
        """
        pass

    @rss.group("rsshub")
    def rsshub(self, event: AstrMessageEvent):
        """RSSHub相关操作

        可以添加、查看、删除RSSHub的端点
        """
        pass

    @rsshub.command("add")
    async def rsshub_add(self, event: AstrMessageEvent, url: str):
        """添加一个RSSHub端点

        Args:
            url: RSSHub服务器地址，例如：https://rsshub.app
        """
        if url.endswith("/"):
            url = url[:-1]
        # 检查是否为url或ip
        if not self._is_url_or_ip(url):
            yield event.plain_result("请输入正确的URL")
            return

        # 添加RSSHub端点
        success = self.data_handler.add_rsshub_endpoint(url)
        if success:
            yield event.plain_result("添加成功")
        else:
            yield event.plain_result("该RSSHub端点已存在")

    @rsshub.command("list")
    async def rsshub_list(self, event: AstrMessageEvent):
        """列出所有已添加的RSSHub端点"""
        endpoints = self.data_handler.get_rsshub_endpoints()
        if not endpoints:
            yield event.plain_result("当前没有添加任何RSSHub端点")
            return

        ret = "当前Bot添加的rsshub endpoint：\n"
        ret += "\n".join([
            f"{i}: {endpoint['url']}"
            for i, endpoint in enumerate(endpoints)
        ])
        yield event.plain_result(ret)

    @rsshub.command("remove")
    async def rsshub_remove(self, event: AstrMessageEvent, idx: int):
        """删除一个RSSHub端点

        Args:
            idx: 要删除的端点索引，可通过list命令查看
        """
        endpoints = self.data_handler.get_rsshub_endpoints()
        if idx < 0 or idx >= len(endpoints):
            yield event.plain_result("索引越界")
            return

        endpoint_id = endpoints[idx]['id']
        success = self.data_handler.remove_rsshub_endpoint(endpoint_id)
        if success:
            yield event.plain_result("删除成功")
        else:
            yield event.plain_result("删除失败")

    @rss.command("add")
    async def add_command(
        self,
        event: AstrMessageEvent,
        idx: int,
        route: str,
        minute: str,
        hour: str,
        day: str,
        month: str,
        day_of_week: str,
    ):
        """通过RSSHub路由添加订阅

        Args:
            idx: RSSHub端点索引，可通过/rss rsshub list查看
            route: RSSHub路由，需以/开头
            minute: Cron表达式分钟字段
            hour: Cron表达式小时字段
            day: Cron表达式日期字段
            month: Cron表达式月份字段
            day_of_week: Cron表达式星期字段
        """
        endpoint_url = self.data_handler.get_rsshub_endpoint_by_index(idx)
        if not endpoint_url:
            yield event.plain_result(
                "索引越界, 请使用 /rss rsshub list 查看已经添加的 rsshub endpoint"
            )
            return
        if not route.startswith("/"):
            yield event.plain_result("路由必须以 / 开头")
            return

        url = endpoint_url + route
        cron_expr = f"{minute} {hour} {day} {month} {day_of_week}"

        ret = await self._add_url(url, cron_expr, event)
        if isinstance(ret, MessageEventResult):
            yield ret
            return
        else:
            chan_title = ret["title"]
            chan_desc = ret["description"]

        # 刷新定时任务
        self._fresh_asyncIOScheduler()

        yield event.plain_result(
            f"添加成功。频道信息：\n标题: {chan_title}\n描述: {chan_desc}"
        )

    @rss.command("add-url")
    async def add_url_command(
        self,
        event: AstrMessageEvent,
        url: str,
        minute: str,
        hour: str,
        day: str,
        month: str,
        day_of_week: str,
    ):
        """直接通过Feed URL添加订阅

        Args:
            url: RSS Feed的完整URL
            minute: Cron表达式分钟字段
            hour: Cron表达式小时字段
            day: Cron表达式日期字段
            month: Cron表达式月份字段
            day_of_week: Cron表达式星期字段
        """
        cron_expr = f"{minute} {hour} {day} {month} {day_of_week}"
        ret = await self._add_url(url, cron_expr, event)
        if isinstance(ret, MessageEventResult):
            yield ret
            return
        else:
            chan_title = ret["title"]
            chan_desc = ret["description"]

        # 刷新定时任务
        self._fresh_asyncIOScheduler()

        yield event.plain_result(
            f"添加成功。频道信息：\n标题: {chan_title}\n描述: {chan_desc}"
        )

    @rss.command("list")
    async def list_command(self, event: AstrMessageEvent):
        """列出当前所有订阅的RSS频道"""
        user_id = event.unified_msg_origin
        subscriptions = self.data_handler.get_user_subscriptions(user_id)

        if not subscriptions:
            yield event.plain_result("当前没有订阅任何频道")
            return

        ret = "当前订阅的频道：\n"
        for i, sub in enumerate(subscriptions):
            title = sub['title'] or '未知频道'
            description = sub['description'] or '无描述'
            ret += f"{i}. {title} - {description}\n"
        yield event.plain_result(ret)

    @rss.command("remove")
    async def remove_command(self, event: AstrMessageEvent, idx: int):
        """删除一个RSS订阅

        Args:
            idx: 要删除的订阅索引，可通过/rss list查看
        """
        user_id = event.unified_msg_origin
        subscriptions = self.data_handler.get_user_subscriptions(user_id)

        if idx < 0 or idx >= len(subscriptions):
            yield event.plain_result("索引越界, 请使用 /rss list 查看已经添加的订阅")
            return

        feed_id = subscriptions[idx]['feed_id']
        success = self.data_handler.remove_subscription(user_id, feed_id)

        if success:
            # 刷新定时任务
            self._fresh_asyncIOScheduler()
            yield event.plain_result("删除成功")
        else:
            yield event.plain_result("删除失败")

    @rss.command("get")
    async def get_command(self, event: AstrMessageEvent, idx: int):
        """获取指定订阅的最新内容

        Args:
            idx: 要查看的订阅索引，可通过/rss list查看
        """
        user_id = event.unified_msg_origin
        subscriptions = self.data_handler.get_user_subscriptions(user_id)

        if idx < 0 or idx >= len(subscriptions):
            yield event.plain_result("索引越界, 请使用 /rss list 查看已经添加的订阅")
            return

        feed_id = subscriptions[idx]['feed_id']
        latest_item = self.data_handler.get_latest_item_for_feed(feed_id)

        if not latest_item:
            yield event.plain_result("没有找到订阅内容")
            return

        # 获取RSS源信息
        feed = self.data_handler.get_feed_by_id(feed_id)
        latest_item['feed_title'] = feed['title'] if feed else '未知频道'

        # 转换为RSSItem对象
        rss_item = self._convert_db_item_to_rss_item(latest_item)

        # 分解MessageSession
        platform_name, message_type, session_id = user_id.split(":")
        # 构造返回消息链
        comps = await self._get_chain_components(rss_item)
        # 区分平台
        if platform_name == "aiocqhttp":
            node = Comp.Node(
                uin=int(session_id),
                name="Astrbot",
                content=comps
            )
            yield event.chain_result([node]).use_t2i(self.t2i)
        else:
            yield event.chain_result(comps).use_t2i(self.t2i)
