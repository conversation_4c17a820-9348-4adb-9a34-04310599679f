#!/usr/bin/env python3
"""
测试SQLite数据库迁移和功能的脚本
"""

import os
import sys
import json
import tempfile
import sqlite3
from data_handler import DataHandler

def test_database_initialization():
    """测试数据库初始化"""
    print("测试数据库初始化...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        
        # 检查数据库文件是否创建
        assert os.path.exists(db_path), "数据库文件未创建"
        
        # 检查表是否创建
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['rsshub_endpoints', 'rss_feeds', 'subscriptions', 'rss_items', 'push_records']
            for table in expected_tables:
                assert table in tables, f"表 {table} 未创建"
        
        print("✅ 数据库初始化测试通过")

def test_rsshub_endpoints():
    """测试RSSHub端点管理"""
    print("测试RSSHub端点管理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        
        # 测试添加端点
        assert handler.add_rsshub_endpoint("https://rsshub.app"), "添加端点失败"
        assert not handler.add_rsshub_endpoint("https://rsshub.app"), "重复添加端点应该失败"
        
        # 测试获取端点
        endpoints = handler.get_rsshub_endpoints()
        assert len(endpoints) == 1, "端点数量不正确"
        assert endpoints[0]['url'] == "https://rsshub.app", "端点URL不正确"
        
        # 测试根据索引获取端点
        url = handler.get_rsshub_endpoint_by_index(0)
        assert url == "https://rsshub.app", "根据索引获取端点失败"
        
        # 测试删除端点
        endpoint_id = endpoints[0]['id']
        assert handler.remove_rsshub_endpoint(endpoint_id), "删除端点失败"
        
        endpoints = handler.get_rsshub_endpoints()
        assert len(endpoints) == 0, "删除后端点数量不正确"
        
        print("✅ RSSHub端点管理测试通过")

def test_rss_feeds():
    """测试RSS源管理"""
    print("测试RSS源管理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        
        # 测试添加RSS源
        feed_id = handler.add_or_update_feed("https://example.com/rss", "测试频道", "测试描述")
        assert feed_id > 0, "添加RSS源失败"
        
        # 测试获取RSS源
        feed = handler.get_feed_by_url("https://example.com/rss")
        assert feed is not None, "获取RSS源失败"
        assert feed['title'] == "测试频道", "RSS源标题不正确"
        assert feed['description'] == "测试描述", "RSS源描述不正确"
        
        # 测试根据ID获取RSS源
        feed_by_id = handler.get_feed_by_id(feed_id)
        assert feed_by_id is not None, "根据ID获取RSS源失败"
        assert feed_by_id['url'] == "https://example.com/rss", "RSS源URL不正确"
        
        print("✅ RSS源管理测试通过")

def test_subscriptions():
    """测试订阅管理"""
    print("测试订阅管理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        
        # 先添加RSS源
        feed_id = handler.add_or_update_feed("https://example.com/rss", "测试频道", "测试描述")
        
        # 测试添加订阅
        user_id = "test_platform:group:123456"
        assert handler.add_subscription(user_id, feed_id, "0 * * * *"), "添加订阅失败"
        
        # 测试获取用户订阅
        subscriptions = handler.get_user_subscriptions(user_id)
        assert len(subscriptions) == 1, "用户订阅数量不正确"
        assert subscriptions[0]['feed_id'] == feed_id, "订阅的feed_id不正确"
        assert subscriptions[0]['cron_expr'] == "0 * * * *", "订阅的cron表达式不正确"
        
        # 测试获取所有订阅
        all_subs = handler.get_all_subscriptions()
        assert len(all_subs) == 1, "所有订阅数量不正确"
        
        # 测试删除订阅
        assert handler.remove_subscription(user_id, feed_id), "删除订阅失败"
        
        subscriptions = handler.get_user_subscriptions(user_id)
        assert len(subscriptions) == 0, "删除后订阅数量不正确"
        
        print("✅ 订阅管理测试通过")

def test_rss_items():
    """测试RSS内容管理"""
    print("测试RSS内容管理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        
        # 先添加RSS源
        feed_id = handler.add_or_update_feed("https://example.com/rss", "测试频道", "测试描述")
        
        # 测试保存RSS条目
        item_id = handler.save_rss_item(
            feed_id, "测试标题", "https://example.com/item1", 
            "测试描述", "Mon, 01 Jan 2024 00:00:00 GMT", 1704067200,
            ["https://example.com/pic1.jpg"]
        )
        assert item_id is not None, "保存RSS条目失败"
        
        # 测试重复保存（应该返回None）
        duplicate_id = handler.save_rss_item(
            feed_id, "测试标题", "https://example.com/item1", 
            "测试描述", "Mon, 01 Jan 2024 00:00:00 GMT", 1704067200,
            ["https://example.com/pic1.jpg"]
        )
        assert duplicate_id is None, "重复保存应该返回None"
        
        # 测试获取最新条目
        latest_item = handler.get_latest_item_for_feed(feed_id)
        assert latest_item is not None, "获取最新条目失败"
        assert latest_item['title'] == "测试标题", "最新条目标题不正确"
        
        print("✅ RSS内容管理测试通过")

def test_push_records():
    """测试推送记录管理"""
    print("测试推送记录管理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        
        # 先添加RSS源和条目
        feed_id = handler.add_or_update_feed("https://example.com/rss", "测试频道", "测试描述")
        item_id = handler.save_rss_item(
            feed_id, "测试标题", "https://example.com/item1", 
            "测试描述", "Mon, 01 Jan 2024 00:00:00 GMT", 1704067200
        )
        
        user_id = "test_platform:group:123456"
        
        # 测试标记推送
        assert handler.mark_item_as_pushed(user_id, item_id), "标记推送失败"
        
        # 测试检查推送状态
        assert handler.is_item_pushed_to_user(user_id, item_id), "检查推送状态失败"
        
        # 测试获取新条目（应该为空，因为已推送）
        handler.add_subscription(user_id, feed_id, "0 * * * *")
        new_items = handler.get_new_items_for_user(user_id, feed_id, 0)
        assert len(new_items) == 0, "已推送的条目不应该出现在新条目中"
        
        print("✅ 推送记录管理测试通过")

def test_json_migration():
    """测试JSON数据迁移"""
    print("测试JSON数据迁移...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建旧的JSON数据文件
        json_path = os.path.join(temp_dir, "old_data.json")
        old_data = {
            "rsshub_endpoints": ["https://rsshub.app", "https://rsshub.example.com"],
            "https://example.com/rss": {
                "info": {
                    "title": "测试频道",
                    "description": "测试描述"
                },
                "subscribers": {
                    "test_platform:group:123456": {
                        "cron_expr": "0 * * * *",
                        "last_update": 1704067200,
                        "latest_link": "https://example.com/item1"
                    }
                }
            }
        }
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(old_data, f, indent=2, ensure_ascii=False)
        
        # 测试迁移
        db_path = os.path.join(temp_dir, "test.db")
        handler = DataHandler(db_path)
        handler.migrate_from_json(json_path)
        
        # 验证迁移结果
        endpoints = handler.get_rsshub_endpoints()
        assert len(endpoints) == 2, "RSSHub端点迁移数量不正确"
        
        feed = handler.get_feed_by_url("https://example.com/rss")
        assert feed is not None, "RSS源迁移失败"
        assert feed['title'] == "测试频道", "RSS源标题迁移不正确"
        
        subscriptions = handler.get_user_subscriptions("test_platform:group:123456")
        assert len(subscriptions) == 1, "订阅迁移数量不正确"
        assert subscriptions[0]['cron_expr'] == "0 * * * *", "订阅cron表达式迁移不正确"
        
        # 检查备份文件是否创建
        backup_path = json_path + ".backup"
        assert os.path.exists(backup_path), "备份文件未创建"
        
        print("✅ JSON数据迁移测试通过")

def main():
    """主测试函数"""
    print("开始测试SQLite数据库重构...")
    print("=" * 60)
    
    try:
        test_database_initialization()
        test_rsshub_endpoints()
        test_rss_feeds()
        test_subscriptions()
        test_rss_items()
        test_push_records()
        test_json_migration()
        
        print("=" * 60)
        print("✅ 所有测试通过！SQLite数据库重构成功。")
        print("\n新功能特性:")
        print("1. ✅ 使用SQLite数据库存储所有数据")
        print("2. ✅ RSSHub端点独立管理")
        print("3. ✅ 每个用户/群组的订阅推送时间独立")
        print("4. ✅ RSS内容保存到数据库，避免重复推送")
        print("5. ✅ 支持从旧JSON数据自动迁移")
        print("6. ✅ 推送记录管理，精确控制推送状态")
        return 0
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
