#!/usr/bin/env python3
"""
基础数据库功能测试脚本（不依赖外部模块）
"""

import os
import sys
import sqlite3
import tempfile
import json

def test_database_creation():
    """测试数据库创建和表结构"""
    print("测试数据库创建和表结构...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        
        # 模拟DataHandler的数据库初始化
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rsshub_endpoints (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rss_feeds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    title TEXT,
                    description TEXT,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    feed_id INTEGER NOT NULL,
                    cron_expr TEXT NOT NULL,
                    last_check_time INTEGER DEFAULT 0,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE,
                    UNIQUE(user_id, feed_id)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rss_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    feed_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    link TEXT NOT NULL,
                    description TEXT,
                    pub_date TEXT,
                    pub_date_timestamp INTEGER,
                    pic_urls TEXT,
                    content_hash TEXT UNIQUE NOT NULL,
                    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS push_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    item_id INTEGER NOT NULL,
                    pushed_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                    FOREIGN KEY (item_id) REFERENCES rss_items (id) ON DELETE CASCADE,
                    UNIQUE(user_id, item_id)
                )
            ''')
            
            conn.commit()
        
        # 验证表是否创建成功
        conn = sqlite3.connect(db_path)
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            expected_tables = ['rsshub_endpoints', 'rss_feeds', 'subscriptions', 'rss_items', 'push_records']
            for table in expected_tables:
                assert table in tables, f"表 {table} 未创建"
        finally:
            conn.close()
        
        print("✅ 数据库创建和表结构测试通过")

def test_basic_operations():
    """测试基本的数据库操作"""
    print("测试基本数据库操作...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        
        # 初始化数据库
        conn = sqlite3.connect(db_path)
        try:
            cursor = conn.cursor()

            # 创建表（简化版）
            cursor.execute('''
                CREATE TABLE rsshub_endpoints (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL
                )
            ''')

            cursor.execute('''
                CREATE TABLE rss_feeds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    title TEXT,
                    description TEXT
                )
            ''')

            # 测试插入RSSHub端点
            cursor.execute('INSERT INTO rsshub_endpoints (url) VALUES (?)', ('https://rsshub.app',))
            endpoint_id = cursor.lastrowid
            assert endpoint_id > 0, "插入RSSHub端点失败"

            # 测试查询RSSHub端点
            cursor.execute('SELECT url FROM rsshub_endpoints WHERE id = ?', (endpoint_id,))
            result = cursor.fetchone()
            assert result[0] == 'https://rsshub.app', "查询RSSHub端点失败"

            # 测试插入RSS源
            cursor.execute('INSERT INTO rss_feeds (url, title, description) VALUES (?, ?, ?)',
                         ('https://example.com/rss', '测试频道', '测试描述'))
            feed_id = cursor.lastrowid
            assert feed_id > 0, "插入RSS源失败"

            # 测试查询RSS源
            cursor.execute('SELECT title, description FROM rss_feeds WHERE id = ?', (feed_id,))
            result = cursor.fetchone()
            assert result[0] == '测试频道', "RSS源标题不正确"
            assert result[1] == '测试描述', "RSS源描述不正确"

            conn.commit()
        finally:
            conn.close()
        
        print("✅ 基本数据库操作测试通过")

def test_foreign_key_constraints():
    """测试外键约束"""
    print("测试外键约束...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        
        conn = sqlite3.connect(db_path)
        try:
            # 启用外键约束
            conn.execute('PRAGMA foreign_keys = ON')
            cursor = conn.cursor()

            # 创建表
            cursor.execute('''
                CREATE TABLE rss_feeds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL
                )
            ''')

            cursor.execute('''
                CREATE TABLE subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    feed_id INTEGER NOT NULL,
                    FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE
                )
            ''')

            # 插入RSS源
            cursor.execute('INSERT INTO rss_feeds (url) VALUES (?)', ('https://example.com/rss',))
            feed_id = cursor.lastrowid

            # 插入订阅
            cursor.execute('INSERT INTO subscriptions (user_id, feed_id) VALUES (?, ?)',
                         ('test_user', feed_id))

            # 验证订阅存在
            cursor.execute('SELECT COUNT(*) FROM subscriptions WHERE feed_id = ?', (feed_id,))
            count = cursor.fetchone()[0]
            assert count == 1, "订阅插入失败"

            # 删除RSS源，应该级联删除订阅
            cursor.execute('DELETE FROM rss_feeds WHERE id = ?', (feed_id,))

            # 验证订阅被级联删除
            cursor.execute('SELECT COUNT(*) FROM subscriptions WHERE feed_id = ?', (feed_id,))
            count = cursor.fetchone()[0]
            assert count == 0, "外键级联删除失败"

            conn.commit()
        finally:
            conn.close()
        
        print("✅ 外键约束测试通过")

def test_unique_constraints():
    """测试唯一约束"""
    print("测试唯一约束...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test.db")
        
        conn = sqlite3.connect(db_path)
        try:
            cursor = conn.cursor()

            # 创建表
            cursor.execute('''
                CREATE TABLE rsshub_endpoints (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL
                )
            ''')

            # 插入第一个端点
            cursor.execute('INSERT INTO rsshub_endpoints (url) VALUES (?)', ('https://rsshub.app',))

            # 尝试插入重复的端点，应该失败
            try:
                cursor.execute('INSERT INTO rsshub_endpoints (url) VALUES (?)', ('https://rsshub.app',))
                assert False, "重复插入应该失败"
            except sqlite3.IntegrityError:
                pass  # 预期的错误

            # 使用INSERT OR IGNORE应该成功但不插入
            cursor.execute('INSERT OR IGNORE INTO rsshub_endpoints (url) VALUES (?)', ('https://rsshub.app',))

            # 验证只有一条记录
            cursor.execute('SELECT COUNT(*) FROM rsshub_endpoints')
            count = cursor.fetchone()[0]
            assert count == 1, "唯一约束测试失败"

            conn.commit()
        finally:
            conn.close()
        
        print("✅ 唯一约束测试通过")

def test_json_structure():
    """测试JSON数据结构兼容性"""
    print("测试JSON数据结构兼容性...")
    
    # 模拟旧的JSON数据结构
    old_data = {
        "rsshub_endpoints": ["https://rsshub.app", "https://rsshub.example.com"],
        "https://example.com/rss": {
            "info": {
                "title": "测试频道",
                "description": "测试描述"
            },
            "subscribers": {
                "test_platform:group:123456": {
                    "cron_expr": "0 * * * *",
                    "last_update": 1704067200,
                    "latest_link": "https://example.com/item1"
                },
                "test_platform:group:789012": {
                    "cron_expr": "0 */2 * * *",
                    "last_update": 1704070800,
                    "latest_link": "https://example.com/item2"
                }
            }
        },
        "https://another.com/feed": {
            "info": {
                "title": "另一个频道",
                "description": "另一个描述"
            },
            "subscribers": {
                "test_platform:group:123456": {
                    "cron_expr": "0 0 * * *",
                    "last_update": 1704067200,
                    "latest_link": "https://another.com/item1"
                }
            }
        }
    }
    
    # 验证数据结构
    assert "rsshub_endpoints" in old_data, "缺少rsshub_endpoints"
    assert len(old_data["rsshub_endpoints"]) == 2, "RSSHub端点数量不正确"
    
    # 统计RSS源和订阅
    rss_feeds = 0
    total_subscriptions = 0
    
    for key, value in old_data.items():
        if key == "rsshub_endpoints":
            continue
        
        rss_feeds += 1
        if "subscribers" in value:
            total_subscriptions += len(value["subscribers"])
    
    assert rss_feeds == 2, "RSS源数量不正确"
    assert total_subscriptions == 3, "订阅总数不正确"
    
    print("✅ JSON数据结构兼容性测试通过")

def main():
    """主测试函数"""
    print("开始基础数据库功能测试...")
    print("=" * 50)
    
    try:
        test_database_creation()
        test_basic_operations()
        test_foreign_key_constraints()
        test_unique_constraints()
        test_json_structure()
        
        print("=" * 50)
        print("✅ 所有基础测试通过！")
        print("\n数据库架构验证:")
        print("1. ✅ 表结构创建正确")
        print("2. ✅ 基本CRUD操作正常")
        print("3. ✅ 外键约束工作正常")
        print("4. ✅ 唯一约束工作正常")
        print("5. ✅ JSON数据结构兼容")
        return 0
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
