# RSS插件修复总结

## 修复的问题

### 1. 停止插件后注册的推送仍然存在的问题

**问题描述：**
- 当插件停止时，AsyncIOScheduler的定时任务没有被正确清理
- 导致即使插件已停止，RSS推送任务仍在后台运行

**修复方案：**
- 在 `RssPlugin` 类中添加了 `__del__()` 方法，用于插件析构时自动清理资源
- 添加了 `cleanup()` 方法，提供手动清理的接口
- 两个方法都会：
  - 移除所有定时任务 (`scheduler.remove_all_jobs()`)
  - 关闭调度器 (`scheduler.shutdown(wait=False)`)
  - 包含异常处理，确保清理过程的稳定性

**修改的文件：**
- `main.py` (第53-74行)

### 2. 添加合并转发配置选项并实现功能

**问题描述：**
- 原代码中只对aiocqhttp平台硬编码使用合并转发
- 其他平台都是单独发送消息
- 用户无法根据需要选择是否使用合并转发

**修复方案：**

#### 配置文件修改
在 `_conf_schema.json` 中添加了 `merge_forward` 配置项：
```json
"merge_forward": {
    "description": "是否使用合并转发",
    "type": "bool",
    "hint": "如果设置为 true，多条RSS更新将合并为一条转发消息发送；如果设置为 false，每条RSS更新将单独发送",
    "obvious_hint": true,
    "default": false
}
```

#### 代码逻辑修改
- 在插件初始化时读取 `merge_forward` 配置
- 修改 `cron_task_callback` 方法中的消息发送逻辑：
  - 当 `merge_forward=true` 且平台为 `aiocqhttp` 时，使用合并转发
  - 其他情况下，每条消息单独发送
- 保持了对不同平台的兼容性

**修改的文件：**
- `_conf_schema.json` (第36-42行)
- `main.py` (第42行，第119-155行)
- `README.md` (第114-120行)

### 3. 修复rsshub_remove方法中的不完整代码

**问题描述：**
- `rsshub_remove` 方法中有一行不完整的代码：`self.scheduler.remove_job()`
- 这会导致语法错误或运行时错误

**修复方案：**
- 移除了不完整的 `remove_job()` 调用
- 改为调用 `_fresh_asyncIOScheduler()` 方法来刷新所有定时任务
- 这样可以确保删除RSSHub端点后，相关的定时任务也被正确清理

**修改的文件：**
- `main.py` (第408-414行)

## 技术细节

### 清理机制的实现
```python
def __del__(self):
    """插件析构时清理资源"""
    try:
        if hasattr(self, 'scheduler') and self.scheduler:
            self.logger.info("RSS插件停止，清理定时任务")
            self.scheduler.remove_all_jobs()
            if self.scheduler.running:
                self.scheduler.shutdown(wait=False)
    except Exception as e:
        if hasattr(self, 'logger'):
            self.logger.error(f"RSS插件清理资源时出错: {str(e)}")
```

### 合并转发逻辑
```python
# 根据配置决定是否使用合并转发
if self.merge_forward and platform_name == "aiocqhttp":
    # 使用合并转发（仅支持aiocqhttp平台）
    nodes = []
    for item in rss_items:
        # 创建Node消息
        # ...
    # 发送合并消息
else:
    # 每个消息单独发送
    for item in rss_items:
        # 发送单独消息
        # ...
```

## 兼容性说明

- 所有修改都保持了向后兼容性
- 新增的 `merge_forward` 配置默认为 `false`，保持原有行为
- 清理机制不会影响正常的插件运行
- 修复的代码错误不会影响现有功能

## 测试建议

1. **插件停止测试：**
   - 启动插件并添加RSS订阅
   - 停止插件
   - 检查定时任务是否被正确清理

2. **合并转发测试：**
   - 设置 `merge_forward=true`
   - 在aiocqhttp平台测试合并转发功能
   - 在其他平台测试单独发送功能
   - 设置 `merge_forward=false` 测试单独发送

3. **RSSHub端点删除测试：**
   - 添加RSSHub端点
   - 删除端点
   - 确认没有语法错误或运行时错误

## 文件变更清单

- ✅ `_conf_schema.json` - 添加merge_forward配置
- ✅ `main.py` - 添加清理机制、实现合并转发逻辑、修复代码错误
- ✅ `README.md` - 更新配置说明文档
- ✅ `FIXES_SUMMARY.md` - 新增修复总结文档
- ✅ `test_fixes.py` - 新增测试脚本
