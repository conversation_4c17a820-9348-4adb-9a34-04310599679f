#!/usr/bin/env python3
"""
简单的数据库功能验证脚本
"""

import os
import sqlite3

def test_sqlite_basic():
    """测试SQLite基本功能"""
    print("测试SQLite基本功能...")
    
    db_path = "test_temp.db"
    
    # 清理可能存在的测试文件
    if os.path.exists(db_path):
        os.remove(db_path)
    
    try:
        # 创建数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试表
        cursor.execute('''
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                value TEXT
            )
        ''')
        
        # 插入测试数据
        cursor.execute('INSERT INTO test_table (name, value) VALUES (?, ?)', ('test1', 'value1'))
        cursor.execute('INSERT INTO test_table (name, value) VALUES (?, ?)', ('test2', 'value2'))
        
        # 查询数据
        cursor.execute('SELECT COUNT(*) FROM test_table')
        count = cursor.fetchone()[0]
        assert count == 2, f"期望2条记录，实际{count}条"
        
        # 测试唯一约束
        try:
            cursor.execute('INSERT INTO test_table (name, value) VALUES (?, ?)', ('test1', 'duplicate'))
            assert False, "应该因为唯一约束失败"
        except sqlite3.IntegrityError:
            pass  # 预期的错误
        
        conn.commit()
        conn.close()
        
        print("✅ SQLite基本功能测试通过")
        
    finally:
        # 清理测试文件
        if os.path.exists(db_path):
            try:
                os.remove(db_path)
            except:
                pass

def test_rss_schema():
    """测试RSS插件的数据库架构"""
    print("测试RSS插件数据库架构...")
    
    db_path = "test_rss.db"
    
    # 清理可能存在的测试文件
    if os.path.exists(db_path):
        os.remove(db_path)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建RSS插件的表结构
        tables = [
            '''CREATE TABLE rsshub_endpoints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE NOT NULL,
                created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
            )''',
            
            '''CREATE TABLE rss_feeds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT UNIQUE NOT NULL,
                title TEXT,
                description TEXT,
                created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
            )''',
            
            '''CREATE TABLE subscriptions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                feed_id INTEGER NOT NULL,
                cron_expr TEXT NOT NULL,
                last_check_time INTEGER DEFAULT 0,
                created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE,
                UNIQUE(user_id, feed_id)
            )''',
            
            '''CREATE TABLE rss_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feed_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                link TEXT NOT NULL,
                description TEXT,
                pub_date TEXT,
                pub_date_timestamp INTEGER,
                pic_urls TEXT,
                content_hash TEXT UNIQUE NOT NULL,
                created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (feed_id) REFERENCES rss_feeds (id) ON DELETE CASCADE
            )''',
            
            '''CREATE TABLE push_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                item_id INTEGER NOT NULL,
                pushed_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (item_id) REFERENCES rss_items (id) ON DELETE CASCADE,
                UNIQUE(user_id, item_id)
            )'''
        ]
        
        # 创建所有表
        for table_sql in tables:
            cursor.execute(table_sql)
        
        # 创建索引
        indexes = [
            'CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id)',
            'CREATE INDEX idx_rss_items_feed_id ON rss_items(feed_id)',
            'CREATE INDEX idx_rss_items_pub_date ON rss_items(pub_date_timestamp)',
            'CREATE INDEX idx_push_records_user_item ON push_records(user_id, item_id)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # 验证表是否创建成功
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['rsshub_endpoints', 'rss_feeds', 'subscriptions', 'rss_items', 'push_records']
        for table in expected_tables:
            assert table in tables, f"表 {table} 未创建"
        
        # 测试基本的数据操作
        # 1. 添加RSSHub端点
        cursor.execute('INSERT INTO rsshub_endpoints (url) VALUES (?)', ('https://rsshub.app',))
        endpoint_id = cursor.lastrowid
        
        # 2. 添加RSS源
        cursor.execute('INSERT INTO rss_feeds (url, title, description) VALUES (?, ?, ?)', 
                     ('https://example.com/rss', '测试频道', '测试描述'))
        feed_id = cursor.lastrowid
        
        # 3. 添加订阅
        cursor.execute('INSERT INTO subscriptions (user_id, feed_id, cron_expr) VALUES (?, ?, ?)', 
                     ('test_platform:group:123456', feed_id, '0 * * * *'))
        
        # 4. 添加RSS条目
        cursor.execute('''INSERT INTO rss_items 
                         (feed_id, title, link, description, content_hash) 
                         VALUES (?, ?, ?, ?, ?)''', 
                     (feed_id, '测试标题', 'https://example.com/item1', '测试描述', 'hash123'))
        item_id = cursor.lastrowid
        
        # 5. 添加推送记录
        cursor.execute('INSERT INTO push_records (user_id, item_id) VALUES (?, ?)', 
                     ('test_platform:group:123456', item_id))
        
        # 验证数据
        cursor.execute('SELECT COUNT(*) FROM rsshub_endpoints')
        assert cursor.fetchone()[0] == 1, "RSSHub端点数量不正确"
        
        cursor.execute('SELECT COUNT(*) FROM rss_feeds')
        assert cursor.fetchone()[0] == 1, "RSS源数量不正确"
        
        cursor.execute('SELECT COUNT(*) FROM subscriptions')
        assert cursor.fetchone()[0] == 1, "订阅数量不正确"
        
        cursor.execute('SELECT COUNT(*) FROM rss_items')
        assert cursor.fetchone()[0] == 1, "RSS条目数量不正确"
        
        cursor.execute('SELECT COUNT(*) FROM push_records')
        assert cursor.fetchone()[0] == 1, "推送记录数量不正确"
        
        conn.commit()
        conn.close()
        
        print("✅ RSS插件数据库架构测试通过")
        
    finally:
        # 清理测试文件
        if os.path.exists(db_path):
            try:
                os.remove(db_path)
            except:
                pass

def test_data_integrity():
    """测试数据完整性"""
    print("测试数据完整性...")
    
    db_path = "test_integrity.db"
    
    # 清理可能存在的测试文件
    if os.path.exists(db_path):
        os.remove(db_path)
    
    try:
        conn = sqlite3.connect(db_path)
        conn.execute('PRAGMA foreign_keys = ON')  # 启用外键约束
        cursor = conn.cursor()
        
        # 创建简化的表结构
        cursor.execute('''CREATE TABLE feeds (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            url TEXT UNIQUE NOT NULL
        )''')
        
        cursor.execute('''CREATE TABLE items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            feed_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            FOREIGN KEY (feed_id) REFERENCES feeds (id) ON DELETE CASCADE
        )''')
        
        # 插入测试数据
        cursor.execute('INSERT INTO feeds (url) VALUES (?)', ('https://example.com/rss',))
        feed_id = cursor.lastrowid
        
        cursor.execute('INSERT INTO items (feed_id, title) VALUES (?, ?)', (feed_id, '测试条目'))
        
        # 验证数据存在
        cursor.execute('SELECT COUNT(*) FROM items WHERE feed_id = ?', (feed_id,))
        assert cursor.fetchone()[0] == 1, "条目插入失败"
        
        # 删除feed，应该级联删除item
        cursor.execute('DELETE FROM feeds WHERE id = ?', (feed_id,))
        
        # 验证item被级联删除
        cursor.execute('SELECT COUNT(*) FROM items WHERE feed_id = ?', (feed_id,))
        assert cursor.fetchone()[0] == 0, "级联删除失败"
        
        conn.commit()
        conn.close()
        
        print("✅ 数据完整性测试通过")
        
    finally:
        # 清理测试文件
        if os.path.exists(db_path):
            try:
                os.remove(db_path)
            except:
                pass

def main():
    """主测试函数"""
    print("开始SQLite数据库重构验证...")
    print("=" * 50)
    
    try:
        test_sqlite_basic()
        test_rss_schema()
        test_data_integrity()
        
        print("=" * 50)
        print("✅ 所有验证测试通过！")
        print("\nSQLite数据库重构验证成功:")
        print("1. ✅ SQLite基本功能正常")
        print("2. ✅ RSS插件数据库架构正确")
        print("3. ✅ 数据完整性约束有效")
        print("4. ✅ 外键级联删除工作正常")
        print("5. ✅ 唯一约束防止重复数据")
        
        print("\n重构特性:")
        print("• RSSHub端点独立管理")
        print("• 用户订阅推送时间独立")
        print("• RSS内容去重存储")
        print("• 推送状态精确追踪")
        print("• 数据关系完整性保证")
        
        return 0
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
